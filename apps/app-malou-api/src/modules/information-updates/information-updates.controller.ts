import { ForbiddenError, subject } from '@casl/ability';
import { NextFunction, Request, Response } from 'express';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import {
    GetDetailedUpdateStatusParams,
    getDetailedUpdateStatusParamsValidator,
    GetMergedInformationUpdateBodyDto,
    getMergedInformationUpdateBodyValidator,
    GetMergedInformationUpdateResponseDto,
    HasRestaurantEverBeenUpdatedBodyDto,
    hasRestaurantEverBeenUpdatedBodyValidator,
    InformationUpdatesStateResponseDto,
    SaveInformationUpdateDataBodyDto,
    saveInformationUpdateDataBodyValidator,
    UpdateInformationUpdatePlatformStateStatusBodyDto,
    updateInformationUpdatePlatformStateStatusBodyValidator,
} from '@malou-io/package-dto';
import { ApiResultV2, CaslAction, CaslSubject } from '@malou-io/package-utils';

import { Body, Params } from ':helpers/decorators/validators';
import { RequestWithPermissions } from ':helpers/utils.types';
import { InformationUpdatesDtoMapper } from ':modules/information-updates/information-updates.dto-mapper';
import { InformationUpdatesUseCases } from ':modules/information-updates/information-updates.use-cases';

import { GetDetailedUpdateStatusesUseCase } from './use-cases/get-detailed-update-statuses/get-detailed-update-statuses.use-case';
import UpdatePlatformStateStatusUseCase from './use-cases/update-platform-state-status/update-platform-state-status.use-case';

@singleton()
export default class InformationUpdatesController {
    constructor(
        private readonly _informationUpdatesDtoMapper: InformationUpdatesDtoMapper,
        private readonly _informationUpdatesUseCases: InformationUpdatesUseCases,
        private readonly _getDetailedUpdateStatusesUseCase: GetDetailedUpdateStatusesUseCase,
        private readonly _updatePlatformStateStatusUseCase: UpdatePlatformStateStatusUseCase
    ) {}

    @Body(saveInformationUpdateDataBodyValidator)
    async handleSaveInformationUpdateData(
        req: RequestWithPermissions<any, any, SaveInformationUpdateDataBodyDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.body;

            // should match handleUpdateRestaurant validation in restaurant.controller.ts
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.UPDATE,
                subject(CaslSubject.RESTAURANT, { _id: restaurantId })
            );

            const data = this._informationUpdatesDtoMapper.toIInformationUpdateData(req.body.data);
            const previousData = this._informationUpdatesDtoMapper.toIInformationUpdateData(req.body.previousData);
            await this._informationUpdatesUseCases.saveInformationUpdateData(restaurantId, data, previousData);
            res.status(200).send({ msg: 'ok' });
        } catch (err) {
            return next(err);
        }
    }

    @Body(updateInformationUpdatePlatformStateStatusBodyValidator)
    async handleUpdateInformationUpdatePlatformStateStatus(
        req: Request<any, any, UpdateInformationUpdatePlatformStateStatusBodyDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId, platformKey, status } = req.body;
            await this._updatePlatformStateStatusUseCase.execute(restaurantId, platformKey, status);
            res.status(200).end();
        } catch (err) {
            return next(err);
        }
    }

    @Body(getMergedInformationUpdateBodyValidator)
    async handleGetMergedInformationUpdateData(
        req: Request<any, any, GetMergedInformationUpdateBodyDto>,
        res: Response<ApiResultV2<GetMergedInformationUpdateResponseDto>>,
        next: NextFunction
    ) {
        try {
            const mergedInformationUpdateData = await this._informationUpdatesUseCases.getMergedInformationUpdateData(req.body);
            const dto = this._informationUpdatesDtoMapper.toMergedInformationUpdateDto(mergedInformationUpdateData);
            res.status(200).json({ data: dto });
        } catch (err) {
            return next(err);
        }
    }

    @Body(hasRestaurantEverBeenUpdatedBodyValidator)
    async handleHasRestaurantEverBeenUpdated(
        req: Request<any, any, HasRestaurantEverBeenUpdatedBodyDto>,
        res: Response<ApiResultV2<boolean>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.body;
            const hasRestaurantEverBeenUpdated: boolean = await this._informationUpdatesUseCases.hasRestaurantEverBeenUpdated(restaurantId);
            res.status(200).json({ data: hasRestaurantEverBeenUpdated });
        } catch (err) {
            return next(err);
        }
    }

    @Params(getDetailedUpdateStatusParamsValidator)
    async handleGetDetailedUpdateStatuses(
        req: Request<GetDetailedUpdateStatusParams>,
        res: Response<InformationUpdatesStateResponseDto>,
        next: NextFunction
    ) {
        try {
            const result = await this._getDetailedUpdateStatusesUseCase.execute(req.params.restaurantId);
            res.status(200).json(result);
        } catch (err) {
            return next(err);
        }
    }
}
