/* Task to update restaurant information (name and menuUrl) and create information updates */
import 'reflect-metadata';

import ':env';

import { assert } from 'console';
import { pickBy } from 'lodash';
import { container, singleton } from 'tsyringe';

import { InformationUpdateDataBodyDto } from '@malou-io/package-dto';
import { IRestaurant, toDbId } from '@malou-io/package-models';
import { DescriptionSize, isNotNil } from '@malou-io/package-utils';

import { InformationUpdatesDtoMapper } from ':modules/information-updates/information-updates.dto-mapper';
import { InformationUpdatesUseCases } from ':modules/information-updates/information-updates.use-cases';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';

interface RestaurantUpdate {
    restaurantId: string;
    newName: string;
    newMenuUrl: string;
}

/**
 * Converts restaurant data to InformationUpdateDataBodyDto format
 * Reproduces the logic from getPreviousDataBodyDtoFromRestaurant in the frontend
 */
function mapRestaurantToInformationUpdateDataBodyDto(restaurant: IRestaurant): InformationUpdateDataBodyDto {
    return pickBy(
        {
            // Basic information
            name: restaurant.name,
            menuUrl: restaurant.menuUrl,
            orderUrl: restaurant.orderUrl,
            reservationUrl: restaurant.reservationUrl,
            website: restaurant.website,

            // Dates
            openingDate: restaurant.openingDate?.toISOString(),

            // Media URLs (logo and cover are ObjectIds in backend, need to be converted to URLs)
            logoUrl: restaurant.logo ? `/api/v1/media/${restaurant.logo}` : undefined,
            coverUrl: restaurant.cover ? `/api/v1/media/${restaurant.cover}` : undefined,

            // Contact info
            phone: restaurant.phone,

            // Category information
            categoryName: restaurant.category ? undefined : undefined, // Category is ObjectId in backend, would need population
            secondaryCategoriesNames: restaurant.categoryList?.length ? undefined : undefined, // CategoryList are ObjectIds, would need population

            // Location
            address: restaurant.address,
            latlng: restaurant.latlng,

            // Social networks
            socialNetworkUrls: restaurant.socialNetworkUrls,

            // Descriptions
            longDescription: restaurant.descriptions?.find((d) => d.size === 'LONG')?.text ?? null,
            shortDescription: restaurant.descriptions?.find((d) => d.size === 'SHORT')?.text ?? null,

            // Hours and status
            isClosedTemporarily: restaurant.isClosedTemporarily,
            regularHours: restaurant.regularHours,
            specialHours: restaurant.specialHours,
            otherHours: restaurant.otherHours?.map((otherHour) => ({
                hoursTypeId: otherHour.hoursType,
                periods: otherHour.periods,
            })),

            // Attributes
            attributes: restaurant.attributeList?.length
                ? restaurant.attributeList
                      .filter((attr) => attr.attributeValue !== 'NOT_CONCERNED')
                      .map((attr) => ({
                          name: attr.attribute ? undefined : 'Unknown', // Attribute is ObjectId, would need population
                          value: attr.attributeValue,
                      }))
                : null,
        },
        isNotNil
    );
}

@singleton()
class UpdateRestaurantInformationTask {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _informationUpdatesDtoMapper: InformationUpdatesDtoMapper,
        private readonly _informationUpdatesUseCases: InformationUpdatesUseCases
    ) {}

    async execute() {
        // Liste des restaurants à mettre à jour avec de fausses données pour le moment
        const restaurantsToUpdate: RestaurantUpdate[] = [
            {
                restaurantId: '507f1f77bcf86cd799439011', // Remplacer par un vrai ID
                newName: 'Restaurant Le Nouveau Nom 1',
                newMenuUrl: 'https://example.com/menu1',
            },
            {
                restaurantId: '507f1f77bcf86cd799439012', // Remplacer par un vrai ID
                newName: 'Restaurant Le Nouveau Nom 2',
                newMenuUrl: 'https://example.com/menu2',
            },
        ];

        for (const update of restaurantsToUpdate) {
            await this._processRestaurantUpdate(update);
        }

        console.log('Mise à jour des restaurants terminée');
    }

    private async _processRestaurantUpdate(update: RestaurantUpdate): Promise<void> {
        try {
            console.log(`Traitement du restaurant ${update.restaurantId}`);

            // 1. Récupérer les données actuelles du restaurant
            const currentRestaurant = await this._restaurantsRepository.findOne({
                filter: { _id: toDbId(update.restaurantId) },
                options: { lean: true },
            });

            assert(currentRestaurant);

            if (!currentRestaurant) {
                console.error(`Restaurant ${update.restaurantId} non trouvé`);
                return;
            }

            // 2. Préparer les données pour l'information update
            const previousDataDto = mapRestaurantToInformationUpdateDataBodyDto(currentRestaurant);
            const previousData = this._informationUpdatesDtoMapper.toIInformationUpdateData(previousDataDto);

            const newDataDto: InformationUpdateDataBodyDto = {
                name: update.newName,
                menuUrl: update.newMenuUrl,
            };
            const newData = this._informationUpdatesDtoMapper.toIInformationUpdateData(newDataDto);

            // 3. Créer l'information update (reproduit la logique de handleSaveInformationUpdateData)
            await this._informationUpdatesUseCases.saveInformationUpdateData(update.restaurantId, newData, previousData);

            console.log(`Information update créée pour le restaurant ${update.restaurantId}`);

            // 4. Mettre à jour les champs dans la base de données
            await this._restaurantsRepository.findOneAndUpdate({
                filter: { _id: toDbId(update.restaurantId) },
                update: {
                    name: update.newName,
                    menuUrl: update.newMenuUrl,
                },
                options: { lean: true },
            });

            console.log(`Restaurant ${update.restaurantId} mis à jour avec succès`);
        } catch (error) {
            console.error(`Erreur lors de la mise à jour du restaurant ${update.restaurantId}:`, error);
        }
    }
}

const task = container.resolve(UpdateRestaurantInformationTask);
task.execute()
    .then(() => {
        console.log('Tâche terminée avec succès');
        process.exit(0);
    })
    .catch((error) => {
        console.error("Erreur lors de l'exécution de la tâche:", error);
        process.exit(1);
    });
